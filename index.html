<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>H<PERSON>ng <PERSON><PERSON></title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
        .tab-active { border-color: #3b82f6; color: #3b82f6; background-color: #eff6ff; }
        .job-done { background-color: #f3f4f6; color: #9ca3af; }
        .job-done input[type="checkbox"] { pointer-events: none; }
        .loader {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 20px auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.7);
            z-index: 9999;
            display: flex;
            justify-content: center;
            align-items: center;
        }
    </style>
</head>
<body class="bg-gray-50 text-gray-800">
    <!-- Overlay cho trạng thái loading toàn màn hình -->
    <div id="loading-overlay" class="overlay hidden">
        <div class="loader"></div>
    </div>


    <div id="app" class="max-w-7xl mx-auto p-4">
        <!-- Thanh Điều Hướng Chính -->
        <header class="bg-white shadow-sm rounded-lg p-4 mb-6">
            <div class="flex justify-between items-center">
                <h1 class="text-2xl font-bold text-gray-900">Hệ Thống Báo Cáo Sản Xuất</h1>
                <div class="flex items-center space-x-2">
                    <div id="script-status" class="px-2 py-1 text-xs rounded bg-yellow-100 text-yellow-800">Đang tải...</div>
                    <button id="debug-btn" class="px-3 py-1 text-xs bg-gray-200 hover:bg-gray-300 rounded">Debug</button>
                </div>
            </div>
            <nav class="mt-4 border-b border-gray-200">
                <div class="flex space-x-4 -mb-px">
                    <button data-tab="plan-cat" class="tab-button py-3 px-4 text-sm font-medium border-b-2 border-transparent hover:border-gray-300 hover:text-gray-700">
                        Plan Cắt Sản Xuất
                    </button>
                    <button data-tab="plan-xa" class="tab-button py-3 px-4 text-sm font-medium border-b-2 border-transparent hover:border-gray-300 hover:text-gray-700">
                        Plan Xả Sản Xuất
                    </button>
                    <button data-tab="report" class="tab-button py-3 px-4 text-sm font-medium border-b-2 border-transparent hover:border-gray-300 hover:text-gray-700">
                        Report
                    </button>
                </div>
            </nav>
        </header>


        <main>
            <!-- Khu vực Plan Cắt Sản Xuất -->
            <div id="page-plan-cat" class="page-content">
                <div class="bg-white p-6 rounded-lg shadow-sm">
                    <h2 class="text-xl font-semibold mb-4">Danh sách công việc CẮT</h2>
                    <div class="grid grid-cols-2 md:grid-cols-5 gap-4 mb-4 p-4 border rounded-lg bg-gray-50">
                        <div>
                            <label for="date-filter-cat" class="block text-sm font-medium text-gray-700">Kế hoạch Ngày</label>
                            <input type="date" id="date-filter-cat" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        </div>
                        <div>
                            <label for="line-filter-cat" class="block text-sm font-medium text-gray-700">Line</label>
                            <select id="line-filter-cat" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                <option value="Tất cả">Tất cả</option>
                            </select>
                        </div>
                        <div>
                            <label for="plan-filter-cat" class="block text-sm font-medium text-gray-700">Plan</label>
                            <select id="plan-filter-cat" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                <option value="Tất cả">Tất cả</option>
                            </select>
                        </div>
                        <div>
                            <label for="mayin-filter-cat" class="block text-sm font-medium text-gray-700">Máy in</label>
                            <select id="mayin-filter-cat" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                <option value="Tất cả">Tất cả</option>
                            </select>
                        </div>
                        <div>
                            <label for="stockf-filter-cat" class="block text-sm font-medium text-gray-700">Stock code F</label>
                            <select id="stockf-filter-cat" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                <option value="Tất cả">Tất cả</option>
                            </select>
                        </div>
                    </div>
                    <div id="job-list-cat" class="overflow-x-auto">
                        <!-- Danh sách công việc Cắt sẽ được hiển thị ở đây -->
                    </div>
                </div>
            </div>


            <!-- Khu vực Plan Xả Sản Xuất -->
            <div id="page-plan-xa" class="page-content hidden">
                 <div class="bg-white p-6 rounded-lg shadow-sm">
                    <h2 class="text-xl font-semibold mb-4">Danh sách công việc XẢ</h2>
                    <div class="grid grid-cols-2 md:grid-cols-5 gap-4 mb-4 p-4 border rounded-lg bg-gray-50">
                        <div>
                            <label for="date-filter-xa" class="block text-sm font-medium text-gray-700">Kế hoạch Ngày</label>
                            <input type="date" id="date-filter-xa" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        </div>
                        <div>
                            <label for="line-filter-xa" class="block text-sm font-medium text-gray-700">Line</label>
                            <select id="line-filter-xa" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                <option value="Tất cả">Tất cả</option>
                            </select>
                        </div>
                        <div>
                            <label for="plan-filter-xa" class="block text-sm font-medium text-gray-700">Plan</label>
                            <select id="plan-filter-xa" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                <option value="Tất cả">Tất cả</option>
                            </select>
                        </div>
                        <div>
                            <label for="mayin-filter-xa" class="block text-sm font-medium text-gray-700">Máy in</label>
                            <select id="mayin-filter-xa" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                <option value="Tất cả">Tất cả</option>
                            </select>
                        </div>
                        <div>
                            <label for="stockf-filter-xa" class="block text-sm font-medium text-gray-700">Stock code F</label>
                            <select id="stockf-filter-xa" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                <option value="Tất cả">Tất cả</option>
                            </select>
                        </div>
                    </div>
                    <div id="job-list-xa" class="overflow-x-auto">
                        <!-- Danh sách công việc Xả sẽ được hiển thị ở đây -->
                    </div>
                </div>
            </div>


            <!-- Khu vực Report -->
            <div id="page-report" class="page-content hidden">
                 <div class="bg-white p-6 rounded-lg shadow-sm">
                    <h2 class="text-xl font-semibold mb-4">Dashboard Báo Cáo</h2>
                    <!-- Filters for Report -->
                    <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-5 gap-4 mb-6 p-4 border rounded-lg bg-gray-50">
                         <div>
                            <label for="report-date" class="block text-sm font-medium text-gray-700">Ngày Báo Cáo</label>
                            <input type="date" id="report-date" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                        </div>
                         <div>
                            <label for="report-type" class="block text-sm font-medium text-gray-700">Loại Kế Hoạch</label>
                            <select id="report-type" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                                <option value="Tất cả">Tất cả</option>
                                <option value="Cắt">Cắt</option>
                                <option value="Xả">Xả</option>
                            </select>
                        </div>
                        <div>
                            <label for="report-line" class="block text-sm font-medium text-gray-700">Line</label>
                            <select id="report-line" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                                <option value="Tất cả">Tất cả</option>
                            </select>
                        </div>
                         <div>
                            <label for="report-plan" class="block text-sm font-medium text-gray-700">Plan</label>
                            <select id="report-plan" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                                <option value="Tất cả">Tất cả</option>
                            </select>
                        </div>
                         <div>
                            <label for="report-ca" class="block text-sm font-medium text-gray-700">Ca</label>
                            <select id="report-ca" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                                <option value="Tất cả">Tất cả</option>
                                <option value="Ca 1">Ca 1</option>
                                <option value="Ca 3">Ca 3</option>
                            </select>
                        </div>
                    </div>


                    <div id="report-loading" class="text-center py-8 hidden">
                        <div class="loader"></div>
                        <p class="text-gray-500">Đang tải dữ liệu...</p>
                    </div>
                   
                    <div id="report-content" class="space-y-8">
                        <!-- Bảng Thông tin chung -->
                        <div>
                            <h3 class="text-lg font-semibold mb-2 text-gray-700">Thông Tin Chung</h3>
                            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                                <div class="bg-gray-100 p-4 rounded-lg">
                                    <p class="text-sm text-gray-600">Nhân lực (Ca 1/Ca 3)</p>
                                    <p id="metric-nhanluc" class="text-2xl font-bold text-gray-800">0/0</p>
                                </div>
                                <div class="bg-gray-100 p-4 rounded-lg">
                                    <p class="text-sm text-gray-600">Target Cắt (Tổng ngày)</p>
                                    <p id="metric-target-cat" class="text-2xl font-bold text-gray-800">0</p>
                                </div>
                                <div class="bg-gray-100 p-4 rounded-lg">
                                    <p class="text-sm text-gray-600">Target Xả (Tổng ngày)</p>
                                    <p id="metric-target-xa" class="text-2xl font-bold text-gray-800">0</p>
                                </div>
                            </div>
                        </div>


                        <!-- Bảng 1: Kế Hoạch -->
                        <div>
                            <h3 class="text-lg font-semibold mb-2 text-blue-600">BÁO CÁO KẾ HOẠCH SẢN XUẤT (Mục tiêu)</h3>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div class="bg-blue-50 p-4 rounded-lg">
                                    <p class="text-sm text-blue-800">Tổng tờ nhỏ (Kế hoạch)</p>
                                    <p id="metric-plan-small" class="text-2xl font-bold text-blue-900">0</p>
                                </div>
                                <div class="bg-blue-50 p-4 rounded-lg">
                                    <p class="text-sm text-blue-800">Tổng tờ lớn (Kế hoạch)</p>
                                    <p id="metric-plan-large" class="text-2xl font-bold text-blue-900">0</p>
                                </div>
                                <div class="bg-orange-50 p-4 rounded-lg">
                                    <p class="text-sm text-orange-800">Tổng tờ nhỏ (Pending)</p>
                                    <p id="metric-plan-pending-small" class="text-2xl font-bold text-orange-900">0</p>
                                </div>
                                <div class="bg-blue-50 p-4 rounded-lg">
                                    <p class="text-sm text-blue-800">Thời gian dự kiến (h)</p>
                                    <p id="metric-plan-time" class="text-2xl font-bold text-blue-900">0.0</p>
                                </div>
                                <div class="bg-blue-50 p-4 rounded-lg">
                                    <p class="text-sm text-blue-800">Thời gian còn lại (h)</p>
                                    <p id="metric-plan-remaining" class="text-2xl font-bold text-blue-900">0.0</p>
                                </div>
                                <div class="bg-gray-100 p-4 rounded-lg">
                                    <p class="text-sm text-gray-600">Downtime (h)</p>
                                    <p id="metric-plan-downtime" class="text-2xl font-bold text-gray-800">0.0</p>
                                </div>
                            </div>
                        </div>


                        <!-- Bảng 2: Output -->
                        <div>
                            <h3 class="text-lg font-semibold mb-2 text-green-600">BÁO CÁO OUTPUT THỰC TẾ (Kết quả)</h3>
                             <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div class="bg-green-50 p-4 rounded-lg">
                                    <p class="text-sm text-green-800">Tổng tờ nhỏ (Output)</p>
                                    <p id="metric-output-small" class="text-2xl font-bold text-green-900">0</p>
                                </div>
                                <div class="bg-green-50 p-4 rounded-lg">
                                    <p class="text-sm text-green-800">Tổng tờ lớn (Output)</p>
                                    <p id="metric-output-large" class="text-2xl font-bold text-green-900">0</p>
                                </div>
                                <div class="bg-green-50 p-4 rounded-lg">
                                    <p class="text-sm text-green-800">Thời gian đã cắt (h)</p>
                                    <p id="metric-output-time" class="text-2xl font-bold text-green-900">0.0</p>
                                </div>
                                 <div class="bg-gray-100 p-4 rounded-lg col-span-1 md:col-span-3">
                                    <p class="text-sm text-gray-600">Downtime (h)</p>
                                    <p id="metric-output-downtime" class="text-2xl font-bold text-gray-800">0.0</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>


    <!-- Khu vực báo cáo cố định -->
    <footer id="report-bar" class="sticky bottom-0 bg-white shadow-lg border-t p-4 hidden">
        <div class="max-w-7xl mx-auto flex flex-col sm:flex-row items-center justify-between gap-4">
            <!-- Hộp thông tin lựa chọn -->
            <div id="selection-summary" class="text-sm text-gray-600 bg-gray-100 p-2 rounded-md hidden">
                <span>Đã chọn: <b id="selected-count">0</b> đơn</span> |
                <span>Tổng tờ nhỏ: <b id="selected-small-sheets">0</b></span>
            </div>
            <div class="flex-grow w-full sm:w-auto">
                <label for="reporter-name" class="sr-only">Tên người báo cáo</label>
                <input type="text" id="reporter-name" placeholder="Nhập tên người báo cáo..." class="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500">
            </div>
            <div class="flex space-x-3 w-full sm:w-auto">
                <button id="btn-report-partial" class="w-full justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-yellow-500 hover:bg-yellow-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500">Báo cáo Một Phần</button>
                <button id="btn-report-done" class="w-full justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">Báo Cáo Hoàn Thành</button>
            </div>
        </div>
    </footer>


    <script>
        // Wait for Google Apps Script client library to load
        function waitForGoogleScript(callback, maxAttempts = 50) {
            let attempts = 0;
            const checkInterval = setInterval(() => {
                attempts++;
                if (typeof google !== 'undefined' && google.script && google.script.run) {
                    clearInterval(checkInterval);
                    callback();
                } else if (attempts >= maxAttempts) {
                    clearInterval(checkInterval);
                    alert('Lỗi: Không thể tải Google Apps Script client library. Vui lòng refresh trang.');
                }
            }, 100);
        }

        document.addEventListener('DOMContentLoaded', () => {
            waitForGoogleScript(() => {
                initializeApp();
            });
        });

        function initializeApp() {
            // Update script status
            const statusEl = document.getElementById('script-status');
            if (checkGoogleScriptStatus()) {
                statusEl.textContent = 'Sẵn sàng';
                statusEl.className = 'px-2 py-1 text-xs rounded bg-green-100 text-green-800';
            } else {
                statusEl.textContent = 'Lỗi';
                statusEl.className = 'px-2 py-1 text-xs rounded bg-red-100 text-red-800';
            }

            // --- TRẠNG THÁI ỨNG DỤNG ---
            let activeTab = 'plan-cat';
            let allJobsData = { cat: [], xa: [] }; // Cache for job data


            // --- CÁC ĐỐI TƯỢNG DOM ---
            const loadingOverlay = document.getElementById('loading-overlay');
            const pages = document.querySelectorAll('.page-content');
            const tabs = document.querySelectorAll('.tab-button');
            const reportBar = document.getElementById('report-bar');
            const dateFilterCat = document.getElementById('date-filter-cat');
            const lineFilterCat = document.getElementById('line-filter-cat');
            const planFilterCat = document.getElementById('plan-filter-cat');
            const mayinFilterCat = document.getElementById('mayin-filter-cat');
            const stockfFilterCat = document.getElementById('stockf-filter-cat');
            const dateFilterXa = document.getElementById('date-filter-xa');
            const lineFilterXa = document.getElementById('line-filter-xa');
            const planFilterXa = document.getElementById('plan-filter-xa');
            const mayinFilterXa = document.getElementById('mayin-filter-xa');
            const stockfFilterXa = document.getElementById('stockf-filter-xa');
            const reportDate = document.getElementById('report-date');
            const reportType = document.getElementById('report-type');
            const reportLine = document.getElementById('report-line');
            const reportPlan = document.getElementById('report-plan');
            const reportCa = document.getElementById('report-ca');
            const selectionSummary = document.getElementById('selection-summary');
            const selectedCountEl = document.getElementById('selected-count');
            const selectedSmallSheetsEl = document.getElementById('selected-small-sheets');


            // --- HÀM TIỆN ÍCH ---
            const showLoading = () => loadingOverlay.classList.remove('hidden');
            const hideLoading = () => loadingOverlay.classList.add('hidden');

            // Check Google Apps Script status
            function checkGoogleScriptStatus() {
                const status = typeof google !== 'undefined' && google.script && google.script.run;
                console.log('Google Apps Script status:', status ? 'Ready' : 'Not Ready');
                return status;
            }


            // --- HÀM KHỞI TẠO ---
            function init() {
                const today = new Date().toISOString().split('T')[0];
                [dateFilterCat, dateFilterXa, reportDate].forEach(el => el.value = today);
                setupEventListeners();
                loadInitialFilters();
                switchTab(activeTab);
            }


            // --- HÀM THIẾT LẬP SỰ KIỆN ---
            function setupEventListeners() {
                tabs.forEach(tab => tab.addEventListener('click', () => switchTab(tab.dataset.tab)));

                // Date filter changes should reload data
                dateFilterCat.addEventListener('change', () => renderJobList('cat'));
                dateFilterXa.addEventListener('change', () => renderJobList('xa'));

                // Other filters just re-display existing data
                [lineFilterCat, planFilterCat, mayinFilterCat, stockfFilterCat].forEach(el => el.addEventListener('change', () => displayJobList(allJobsData.cat, 'cat')));
                [lineFilterXa, planFilterXa, mayinFilterXa, stockfFilterXa].forEach(el => el.addEventListener('change', () => displayJobList(allJobsData.xa, 'xa')));
                document.getElementById('btn-report-partial').addEventListener('click', () => handleReport('Partial'));
                document.getElementById('btn-report-done').addEventListener('click', () => handleReport('Done'));
                [reportDate, reportType, reportLine, reportPlan, reportCa].forEach(filter => {
                    filter.addEventListener('change', fetchReportData);
                });

                // Debug button
                document.getElementById('debug-btn').addEventListener('click', () => {
                    console.log('Running debug test...');

                    // Check Google Apps Script availability
                    if (typeof google === 'undefined' || !google.script || !google.script.run) {
                        alert('Google Apps Script chưa sẵn sàng. Vui lòng refresh trang.');
                        return;
                    }

                    try {
                        google.script.run
                            .withSuccessHandler(result => {
                                console.log('Debug result:', result);
                                alert(`Debug test completed:\n${JSON.stringify(result, null, 2)}`);
                            })
                            .withFailureHandler(error => {
                                console.error('Debug test failed:', error);
                                alert(`Debug test failed: ${error.message || error.toString()}`);
                            })
                            .testDataLoading();
                    } catch (error) {
                        console.error('Exception in debug test:', error);
                        alert(`Debug test exception: ${error.message}`);
                    }
                });
            }
           
            // --- HÀM ĐIỀN DỮ LIỆU CHO BỘ LỌC ---
            function loadInitialFilters() {
                // Check if Google Apps Script is available
                if (typeof google === 'undefined' || !google.script || !google.script.run) {
                    console.error("Google Apps Script chưa sẵn sàng");
                    setTimeout(() => loadInitialFilters(), 500); // Retry after 500ms
                    return;
                }

                try {
                    google.script.run
                        .withSuccessHandler(data => {
                            if (data && data.error) {
                                console.error("Lỗi tải bộ lọc:", data.error);
                                alert("Lỗi khi tải dữ liệu bộ lọc: " + data.error);
                                return;
                            }
                            const populateSelect = (selectElement, items) => {
                                if (!items || !Array.isArray(items)) return;
                                items.forEach(item => {
                                    if(item) {
                                        const option = document.createElement('option');
                                        option.value = item;
                                        option.textContent = item;
                                        selectElement.appendChild(option);
                                    }
                                });
                            };
                            populateSelect(reportLine, data.lines || []);
                            populateSelect(lineFilterCat, data.lines || []);
                            populateSelect(lineFilterXa, data.lines || []);


                            populateSelect(reportPlan, data.plans || []);
                            populateSelect(planFilterCat, data.plans || []);
                            populateSelect(planFilterXa, data.plans || []);

                            populateSelect(mayinFilterCat, data.mayIns || []);
                            populateSelect(mayinFilterXa, data.mayIns || []);


                            populateSelect(stockfFilterCat, data.stockFs || []);
                            populateSelect(stockfFilterXa, data.stockFs || []);
                        })
                        .withFailureHandler(error => {
                            console.error("Lỗi khi tải bộ lọc:", error);
                            alert("Lỗi khi tải dữ liệu bộ lọc: " + (error.message || error.toString()));
                        })
                        .getInitialFilterData();
                } catch (error) {
                    console.error("Exception in loadInitialFilters:", error);
                    alert("Lỗi JavaScript khi tải bộ lọc: " + error.message);
                }
            }


            // --- HÀM CHUYỂN TAB ---
            function switchTab(tabId) {
                activeTab = tabId;
                pages.forEach(page => page.classList.add('hidden'));
                tabs.forEach(tab => tab.classList.remove('tab-active'));


                document.getElementById(`page-${tabId}`).classList.remove('hidden');
                document.querySelector(`[data-tab="${tabId}"]`).classList.add('tab-active');
               
                updateSelectionSummary(null);


                if (tabId === 'plan-cat') {
                    reportBar.classList.remove('hidden');
                    renderJobList('cat');
                } else if (tabId === 'plan-xa') {
                    reportBar.classList.remove('hidden');
                    renderJobList('xa');
                } else {
                    reportBar.classList.add('hidden');
                    fetchReportData();
                }
            }
           
            // --- HÀM CẬP NHẬT TÓM TẮT LỰA CHỌN ---
            function updateSelectionSummary(type) {
                if (!type) {
                    selectionSummary.classList.add('hidden');
                    return;
                }
               
                const data = allJobsData[type];
                const checkedBoxes = document.querySelectorAll(`.job-checkbox-${type}:checked`);
               
                if (checkedBoxes.length > 0) {
                    let totalSmallSheets = 0;
                    checkedBoxes.forEach(box => {
                        const id = box.dataset.id;
                        const job = data.find(j => j.ID === id);
                        if (job) {
                            totalSmallSheets += job["Tổng số tờ nhỏ"];
                        }
                    });
                    selectedCountEl.textContent = checkedBoxes.length;
                    selectedSmallSheetsEl.textContent = totalSmallSheets.toLocaleString();
                    selectionSummary.classList.remove('hidden');
                } else {
                    selectionSummary.classList.add('hidden');
                }
            }


            // --- HÀM HIỂN THỊ DANH SÁCH CÔNG VIỆC ---
            function renderJobList(type) {
                const listElement = document.getElementById(`job-list-${type}`);
                listElement.innerHTML = `<div class="text-center py-4"><div class="loader"></div><p class="text-gray-500 mt-2">Đang tải dữ liệu...</p></div>`;

                const dateVal = type === 'cat' ? dateFilterCat.value : dateFilterXa.value;

                if (!dateVal) {
                    listElement.innerHTML = `<p class="text-yellow-500 text-center py-4">Vui lòng chọn ngày để xem danh sách công việc.</p>`;
                    return;
                }

                // Check if Google Apps Script is available
                if (typeof google === 'undefined' || !google.script || !google.script.run) {
                    listElement.innerHTML = `<p class="text-red-500 text-center py-4">Lỗi: Google Apps Script chưa sẵn sàng. Vui lòng refresh trang.</p>`;
                    return;
                }

                console.log(`Loading jobs for ${type} on date: ${dateVal}`);

                try {
                    google.script.run
                        .withSuccessHandler(jobs => {
                            console.log(`Received jobs for ${type}:`, jobs);

                            if (jobs && jobs.error) {
                                console.error('Server error:', jobs.error);
                                listElement.innerHTML = `<p class="text-red-500 text-center py-4">Lỗi từ server: ${jobs.error}</p>`;
                                return;
                            }

                            if (!Array.isArray(jobs)) {
                                console.error('Invalid jobs data:', jobs);
                                listElement.innerHTML = `<p class="text-red-500 text-center py-4">Dữ liệu trả về không hợp lệ. Kiểu dữ liệu: ${typeof jobs}</p>`;
                                return;
                            }

                            allJobsData[type] = jobs;
                            displayJobList(jobs, type);
                        })
                        .withFailureHandler(err => {
                            console.error('Script execution failed:', err);
                            listElement.innerHTML = `<p class="text-red-500 text-center py-4">Lỗi khi gọi server: ${err.message || err.toString()}</p>`;
                        })
                        .getJobs(type === 'cat' ? 'Cắt' : 'Xả', dateVal);
                } catch (error) {
                    console.error('Exception in renderJobList:', error);
                    listElement.innerHTML = `<p class="text-red-500 text-center py-4">Lỗi JavaScript: ${error.message}</p>`;
                }
            }
           
            function displayJobList(jobs, type) {
                const listElement = document.getElementById(`job-list-${type}`);

                if (!jobs || !Array.isArray(jobs)) {
                    listElement.innerHTML = `<p class="text-center text-red-500 py-4">Dữ liệu không hợp lệ.</p>`;
                    return;
                }

                const lineVal = type === 'cat' ? lineFilterCat.value : lineFilterXa.value;
                const planVal = type === 'cat' ? planFilterCat.value : planFilterXa.value;
                const mayinVal = type === 'cat' ? mayinFilterCat.value : mayinFilterXa.value;
                const stockfVal = type === 'cat' ? stockfFilterCat.value : stockfFilterXa.value;


                let filteredData = jobs.filter(job => {
                    if (!job) return false;
                    const lineMatch = (lineVal === 'Tất cả' || job.Line === lineVal);
                    const planMatch = (planVal === 'Tất cả' || job.Plan === planVal);
                    const mayinMatch = (mayinVal === 'Tất cả' || job["Máy in"] === mayinVal);
                    const stockfMatch = (stockfVal === 'Tất cả' || job["Stock code F"] === stockfVal);
                    return lineMatch && planMatch && mayinMatch && stockfMatch;
                });


                if (filteredData.length === 0) {
                    listElement.innerHTML = `<p class="text-center text-gray-500 py-4">Không có công việc nào cho ngày này hoặc bộ lọc đã chọn.</p>`;
                    return;
                }
               
                const headers = ["Chọn", "Line", "Plan", "ID", "BO", "Máy in", "Stock code F", "Tờ nhỏ", "Tờ lớn", "Xả", "Stock size", "Note", "Trạng thái", "Tên nhân viên báo cáo", "Thời gian báo cáo"];
                const dataKeys = ["Line", "Plan", "ID", "BO", "Máy in", "Stock code F", "Tổng số tờ nhỏ", "Tổng số tờ lớn", "Xả", "Stock size", "Note"];


                listElement.innerHTML = `
                    <table class="min-w-full bg-white divide-y divide-gray-200 text-sm">
                        <thead class="bg-gray-50">
                            <tr>
                                ${headers.map(h => `<th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">${h}</th>`).join('')}
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            ${filteredData.map(job => {
                                const reportTime = job['Thời gian báo cáo'] ? (typeof job['Thời gian báo cáo'] === 'string' ? job['Thời gian báo cáo'] : new Date(job['Thời gian báo cáo']).toLocaleString('vi-VN')) : '';
                                const reporterName = job['Tên nhân viên báo cáo'] || '';
                                return `
                                <tr class="${job['Trạng thái'] === 'Done' ? 'job-done' : ''}">
                                    <td class="px-3 py-2"><input type="checkbox" data-id="${job.ID}" class="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 job-checkbox-${type}"></td>
                                    ${dataKeys.map(key => `<td class="px-3 py-2 whitespace-nowrap">${job[key] || ''}</td>`).join('')}
                                    <td class="px-3 py-2 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                                            job['Trạng thái'] === 'Done' ? 'bg-green-100 text-green-800' :
                                            job['Trạng thái'] === 'Partial' ? 'bg-yellow-100 text-yellow-800' :
                                            'bg-red-100 text-red-800'
                                        }">${job['Trạng thái']}</span>
                                    </td>
                                    <td class="px-3 py-2 whitespace-nowrap truncate max-w-xs" title="${reporterName}">${reporterName}</td>
                                    <td class="px-3 py-2 whitespace-nowrap">${reportTime}</td>
                                </tr>
                            `}).join('')}
                        </tbody>
                    </table>`;
               
                document.querySelectorAll(`.job-checkbox-${type}`).forEach(box => {
                    box.addEventListener('change', () => updateSelectionSummary(type));
                });
            }


            // --- HÀM XỬ LÝ BÁO CÁO ---
            function handleReport(reportType) {
                const reporterName = document.getElementById('reporter-name').value.trim();
                if (!reporterName) {
                    alert('Vui lòng nhập tên người báo cáo.');
                    return;
                }


                const jobTypeIdentifier = (activeTab === 'plan-cat') ? 'cat' : 'xa';
                const selectedCheckboxes = document.querySelectorAll(`.job-checkbox-${jobTypeIdentifier}:checked`);
               
                if (selectedCheckboxes.length === 0) {
                    alert('Vui lòng chọn ít nhất một công việc để báo cáo.');
                    return;
                }


                const jobTypeDisplayName = (jobTypeIdentifier === 'cat') ? 'Cắt' : 'Xả';
                const selectedIds = Array.from(selectedCheckboxes).map(cb => cb.dataset.id);
                const reportData = { jobType: jobTypeDisplayName, reporterName, reportType, ids: selectedIds };


                // Check if Google Apps Script is available
                if (typeof google === 'undefined' || !google.script || !google.script.run) {
                    alert('Google Apps Script chưa sẵn sàng. Vui lòng refresh trang.');
                    return;
                }

                showLoading();
                try {
                    google.script.run
                        .withSuccessHandler(response => {
                            hideLoading();
                            if (response && response.status === 'success') {
                                alert(response.message);
                                document.getElementById('reporter-name').value = '';
                                renderJobList(jobTypeIdentifier);
                                updateSelectionSummary(jobTypeIdentifier);
                            } else {
                                alert('Lỗi từ server: ' + (response ? response.message : 'Không có phản hồi từ server'));
                            }
                        })
                        .withFailureHandler(err => {
                            hideLoading();
                            alert('Lỗi hệ thống khi báo cáo: ' + (err.message || err.toString()));
                        })
                        .processReport(reportData);
                } catch (error) {
                    hideLoading();
                    console.error('Exception in handleReport:', error);
                    alert('Lỗi JavaScript khi báo cáo: ' + error.message);
                }
            }


            // --- HÀM LẤY DỮ LIỆU BÁO CÁO ---
            function fetchReportData() {
                const loadingEl = document.getElementById('report-loading');
                const contentEl = document.getElementById('report-content');

                loadingEl.classList.remove('hidden');
                contentEl.classList.add('hidden');

                // Check if Google Apps Script is available
                if (typeof google === 'undefined' || !google.script || !google.script.run) {
                    loadingEl.classList.add('hidden');
                    alert('Google Apps Script chưa sẵn sàng. Vui lòng refresh trang.');
                    return;
                }

                const filters = {
                    date: reportDate.value,
                    type: reportType.value,
                    line: reportLine.value,
                    plan: reportPlan.value,
                    ca: reportCa.value
                };

                try {
                    google.script.run
                        .withSuccessHandler(data => {
                            if (data && data.error) {
                                alert('Lỗi khi lấy dữ liệu báo cáo: ' + data.error);
                                loadingEl.classList.add('hidden');
                                return;
                            }
                            displayReportData(data);
                        })
                        .withFailureHandler(err => {
                             alert('Lỗi hệ thống khi tải báo cáo: ' + (err.message || err.toString()));
                             loadingEl.classList.add('hidden');
                        })
                        .getDashboardData(filters);
                } catch (error) {
                    console.error('Exception in fetchReportData:', error);
                    alert('Lỗi JavaScript khi tải báo cáo: ' + error.message);
                    loadingEl.classList.add('hidden');
                }
            }


            function displayReportData(data) {
                const loadingEl = document.getElementById('report-loading');
                const contentEl = document.getElementById('report-content');
               
                document.getElementById('metric-nhanluc').textContent = `${data.nhanLuc.ngay}/${data.nhanLuc.dem}`;
                document.getElementById('metric-target-cat').textContent = data.targets.cat.toLocaleString();
                document.getElementById('metric-target-xa').textContent = data.targets.xa.toLocaleString();


                document.getElementById('metric-plan-small').textContent = data.plan.totalSmall.toLocaleString();
                document.getElementById('metric-plan-large').textContent = data.plan.totalLarge.toLocaleString();
                document.getElementById('metric-plan-pending-small').textContent = data.plan.pendingSmall.toLocaleString();
                document.getElementById('metric-plan-time').textContent = data.plan.estimatedTime.toFixed(1);
                document.getElementById('metric-plan-remaining').textContent = data.plan.remainingTime.toFixed(1);
                document.getElementById('metric-plan-downtime').textContent = data.plan.downtime.toFixed(1);
               
                document.getElementById('metric-output-small').textContent = data.output.totalSmall.toLocaleString();
                document.getElementById('metric-output-large').textContent = data.output.totalLarge.toLocaleString();
                document.getElementById('metric-output-time').textContent = data.output.actualTime.toFixed(1);
                document.getElementById('metric-output-downtime').textContent = data.output.downtime.toFixed(1);


                loadingEl.classList.add('hidden');
                contentEl.classList.remove('hidden');
            }


            // --- KHỞI CHẠY ỨNG DỤNG ---
            init();
        } // End of initializeApp function
    </script>
</body>
</html>



