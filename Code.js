// ===============================================================
// KHAI BÁO BIẾN TOÀN CỤC VÀ HẰNG SỐ
// ===============================================================
const ss = SpreadsheetApp.getActiveSpreadsheet();
const SHEET_CAT = "DanhSachCat";
const SHEET_XA = "DanhSachXa";
const SHEET_NHAT_KY_BAO_CAO = "NhatKyBaoCao";
const SHEET_NHAN_LUC = "NhanLucTheoNgay";
const SHEET_DOWNTIME_LOG = "NhatKyHoatDongMay";
const SHEET_DEBUG_LOG = "DebugLog";


const TARGET_CAT_DAY = 26000;
const TARGET_XA_DAY = 13000;
const TARGET_CAT_SHIFT = 13000; // Target per shift for Cắt
const TARGET_XA_SHIFT = 6500;   // Target per shift for Xả
const STANDARD_WORKFORCE_PER_SHIFT = 6;
const STANDARD_WORKFORCE = 6;


// ===============================================================
// HÀM CHÍNH ĐỂ HIỂN THỊ GIAO DIỆN WEB
// ===============================================================
function doGet(e) {
  return HtmlService.createHtmlOutputFromFile('index')
    .setTitle("Hệ Thống Báo Cáo Sản Xuất")
    .setXFrameOptionsMode(HtmlService.XFrameOptionsMode.ALLOWALL);
}


// ===============================================================
// CÁC HÀM TIỆN ÍCH (HELPER FUNCTIONS)
// ===============================================================
function getSheetData(sheetName) {
  const sheet = ss.getSheetByName(sheetName);
  if (!sheet || sheet.getLastRow() < 2) return [];
  const values = sheet.getDataRange().getValues();
  const headers = values.shift();
  return values.map(row => {
    let obj = {};
    headers.forEach((header, i) => {
      obj[header] = row[i];
    });
    return obj;
  });
}


function formatDate(dateInput) {
  if (!dateInput) return '';
  try {
    const timeZone = Session.getScriptTimeZone();
    const formattedDate = Utilities.formatDate(new Date(dateInput), timeZone, 'yyyy-MM-dd');
    return formattedDate;
  } catch (e) {
    console.log(`formatDate fallback for: ${dateInput}`);
    // Fallback for non-standard date strings
    const dateStr = String(dateInput).split(' ')[0];
    const monthMap = { Jan: '01', Feb: '02', Mar: '03', Apr: '04', May: '05', Jun: '06', Jul: '07', Aug: '08', Sep: '09', Oct: '10', Nov: '11', Dec: '12' };
    let parts = dateStr.split('-');
    if (parts.length === 3 && monthMap[parts[1]]) {
      return `${parts[2]}-${monthMap[parts[1]]}-${parts[0].padStart(2, '0')}`;
    }
    console.log(`formatDate failed for: ${dateInput}`);
    return ''; // Return empty if format is still not recognized
  }
}




// ===============================================================
// CÁC HÀM ĐƯỢC GỌI TỪ GIAO DIỆN WEB (FRONTEND)
// ===============================================================


function getInitialFilterData() {
  try {
    const dataCat = getSheetData(SHEET_CAT);
    const dataXa = getSheetData(SHEET_XA);
    const allJobs = [...dataCat, ...dataXa];


    const lines = [...new Set(allJobs.map(job => job.Line).filter(Boolean))];
    const plans = [...new Set(allJobs.map(job => job.Plan).filter(Boolean))];
    const mayIns = [...new Set(allJobs.map(job => job["Máy in"]).filter(Boolean))];
    const stockFs = [...new Set(allJobs.map(job => job["Stock code F"]).filter(Boolean))];


    return {
      lines: lines.sort(),
      plans: plans.sort(),
      mayIns: mayIns.sort(),
      stockFs: stockFs.sort()
    };
  } catch (e) {
    return { error: e.message };
  }
}




function getJobs(jobType, dateString) {
  try {
    console.log(`getJobs called with jobType: ${jobType}, dateString: ${dateString}`);

    const sheetName = (jobType === 'Cắt') ? SHEET_CAT : SHEET_XA;
    console.log(`Looking for sheet: ${sheetName}`);

    const sheet = ss.getSheetByName(sheetName);
    if (!sheet) {
      console.error(`Sheet not found: ${sheetName}`);
      throw new Error(`Không tìm thấy sheet: ${sheetName}`);
    }

    const lastRow = sheet.getLastRow();
    console.log(`Sheet ${sheetName} has ${lastRow} rows`);

    if (lastRow < 2) {
      console.log(`Sheet ${sheetName} is empty or has no data rows`);
      return [];
    }


    const dataRange = sheet.getDataRange().getValues();
    const headers = dataRange.shift();
    console.log(`Headers found: ${headers.join(', ')}`);

    const dateColIndex = headers.indexOf("Kế hoạch Ngày");
    console.log(`Date column index: ${dateColIndex}`);


    if (dateColIndex === -1) {
      console.error("Date column 'Kế hoạch Ngày' not found");
      throw new Error("Không tìm thấy cột 'Kế hoạch Ngày'");
    }

    const jobs = [];
    dataRange.forEach((row, index) => {
      const cellValue = row[dateColIndex];
      if (cellValue) {
        const formattedCellDate = formatDate(cellValue);
        // Only log first few rows to avoid spam
        if (index < 3) {
          console.log(`Row ${index + 2}: cellValue=${cellValue}, formatted=${formattedCellDate}, target=${dateString}`);
        }

        // If no specific date filter, return all jobs, otherwise filter by date
        if (!dateString || formattedCellDate === dateString) {
          let jobObject = {};
          headers.forEach((header, headerIndex) => {
            jobObject[header] = row[headerIndex];
          });
          jobs.push(jobObject);
        }
      }
    });

    console.log(`getJobs: Found ${jobs.length} jobs for ${jobType} on ${dateString}`);
    return jobs;
  } catch (e) {
    console.error(`getJobs Error: ${e.message}`);
    console.error(`getJobs Stack: ${e.stack}`);
    return { error: e.message };
  }
}


function processReport(reportData) {
  try {
    const { jobType, reporterName, reportType, ids } = reportData;
    const sheetName = (jobType === 'Cắt') ? SHEET_CAT : SHEET_XA;
    const sheet = ss.getSheetByName(sheetName);
    const logSheet = ss.getSheetByName(SHEET_NHAT_KY_BAO_CAO);


    if (!sheet || !logSheet) throw new Error("Không tìm thấy sheet cần thiết.");


    const data = sheet.getDataRange().getValues();
    const headers = data[0];
    const idColIndex = headers.indexOf("ID");
    const statusColIndex = headers.indexOf("Trạng thái");
    const reporterColIndex = headers.indexOf("Tên nhân viên báo cáo");
    const timeColIndex = headers.indexOf("Thời gian báo cáo");
    const caColIndex = headers.indexOf("Ca");
    const outputDateColIndex = headers.indexOf("Output Ngày");


    const now = new Date();
    const currentHour = now.getHours();
    const ca = (currentHour >= 6 && currentHour < 18) ? "Ca 1" : "Ca 3";
   
    let outputDate = new Date(now);
    if (currentHour < 6) {
      outputDate.setDate(outputDate.getDate() - 1);
    }


    ids.forEach(id => {
      for (let i = 1; i < data.length; i++) {
        if (data[i][idColIndex] == id) {
          sheet.getRange(i + 1, statusColIndex + 1).setValue(reportType);
         
          let currentReporters = sheet.getRange(i + 1, reporterColIndex + 1).getValue();
          let newReporters = currentReporters ? `${currentReporters}, ${reporterName}` : reporterName;
          sheet.getRange(i + 1, reporterColIndex + 1).setValue(newReporters);


          sheet.getRange(i + 1, timeColIndex + 1).setValue(now);
          sheet.getRange(i + 1, caColIndex + 1).setValue(ca);
          sheet.getRange(i + 1, outputDateColIndex + 1).setValue(outputDate);


          logSheet.appendRow([jobType, now, reporterName, ca, id, reportType]);
          break;
        }
      }
    });


    return { status: "success", message: `Đã báo cáo thành công ${ids.length} công việc.` };
  } catch (e) {
    console.error(`processReport Error: ${e.stack}`);
    return { status: "error", message: e.message };
  }
}


function getDashboardData(filters) {
  try {
    const dataCat = getSheetData(SHEET_CAT);
    const dataXa = getSheetData(SHEET_XA);
    const dataNhanLuc = getSheetData(SHEET_NHAN_LUC);


    let jobsToConsider = [];
    if (filters.type === 'Cắt' || filters.type === 'Tất cả') jobsToConsider.push(...dataCat.map(j => ({...j, type: 'Cắt'})));
    if (filters.type === 'Xả' || filters.type === 'Tất cả') jobsToConsider.push(...dataXa.map(j => ({...j, type: 'Xả'})));


    let filteredJobs = jobsToConsider.filter(job => {
        const lineMatch = (filters.line === 'Tất cả' || job.Line === filters.line);
        const planMatch = (filters.plan === 'Tất cả' || job.Plan === filters.plan);
        const caMatch = (filters.ca === 'Tất cả' || job.Ca === filters.ca);
        return lineMatch && planMatch && caMatch;
    });


    const nhanLucInfo = dataNhanLuc.find(nl => formatDate(new Date(nl['Ngày'])) === filters.date) || { 'Nhân viên Ca Ngày': 6, 'Nhân viên Ca Đêm': 6 };
    const nhanVienCaNgay = Number(nhanLucInfo['Nhân viên Ca Ngày']) || 0;
    const nhanVienCaDem = Number(nhanLucInfo['Nhân viên Ca Đêm']) || 0;


    const targetCatTotal = (nhanVienCaNgay / STANDARD_WORKFORCE_PER_SHIFT * TARGET_CAT_DAY) + (nhanVienCaDem / STANDARD_WORKFORCE_PER_SHIFT * TARGET_CAT_DAY);
    const targetXaTotal = (nhanVienCaNgay / STANDARD_WORKFORCE_PER_SHIFT * TARGET_XA_DAY) + (nhanVienCaDem / STANDARD_WORKFORCE_PER_SHIFT * TARGET_XA_DAY);


    // --- Logic tính toán Pending ---
    const selectedDate = new Date(filters.date + 'T00:00:00Z');
    const dayOfWeek = selectedDate.getUTCDay();
    let pendingDates = [];
    if (dayOfWeek === 1) { // Monday
        for (let i = 1; i <= 3; i++) {
            let prevDate = new Date(selectedDate);
            prevDate.setUTCDate(selectedDate.getUTCDate() - i);
            pendingDates.push(formatDate(prevDate));
        }
    } else if (dayOfWeek === 0) { // Sunday
        for (let i = 1; i <= 2; i++) {
            let prevDate = new Date(selectedDate);
            prevDate.setUTCDate(selectedDate.getUTCDate() - i);
            pendingDates.push(formatDate(prevDate));
        }
    } else {
        let prevDate = new Date(selectedDate);
        prevDate.setUTCDate(selectedDate.getUTCDate() - 1);
        pendingDates.push(formatDate(prevDate));
    }


    const allJobsForPending = [...dataCat, ...dataXa];
    const pendingJobs = allJobsForPending.filter(job => pendingDates.includes(formatDate(job["Kế hoạch Ngày"])) && job["Trạng thái"] === 'Chờ cắt');
    const pendingSmallTotal = pendingJobs.reduce((sum, j) => sum + (Number(j["Tổng số tờ nhỏ"]) || 0), 0);


    // --- Bảng 1: Kế hoạch ---
    const planJobsToday = filteredJobs.filter(j => formatDate(j["Kế hoạch Ngày"]) === filters.date);
    const pendingJobsFiltered = filteredJobs.filter(j => pendingDates.includes(formatDate(j["Kế hoạch Ngày"])) && j["Trạng thái"] === 'Chờ cắt');
    const planJobs = [...planJobsToday, ...pendingJobsFiltered];


    const planSmallCat = planJobs.filter(j => j.type === 'Cắt').reduce((sum, j) => sum + (Number(j["Tổng số tờ nhỏ"]) || 0), 0);
    const planLargeCat = planJobs.filter(j => j.type === 'Cắt').reduce((sum, j) => sum + (Number(j["Tổng số tờ lớn"]) || 0), 0);
    const planSmallXa = planJobs.filter(j => j.type === 'Xả').reduce((sum, j) => sum + (Number(j["Tổng số tờ nhỏ"]) || 0), 0);
    const planLargeXa = planJobs.filter(j => j.type === 'Xả').reduce((sum, j) => sum + (Number(j["Tổng số tờ lớn"]) || 0), 0);
   
    const planTime = (targetCatTotal > 0 ? (planSmallCat / targetCatTotal) * 24 : 0) + (targetXaTotal > 0 ? (planSmallXa / targetXaTotal) * 24 : 0);


    const remainingJobs = planJobs.filter(j => j["Trạng thái"] === 'Chờ cắt');
    const remainingSmallCat = remainingJobs.filter(j => j.type === 'Cắt').reduce((sum, j) => sum + (Number(j["Tổng số tờ nhỏ"]) || 0), 0);
    const remainingSmallXa = remainingJobs.filter(j => j.type === 'Xả').reduce((sum, j) => sum + (Number(j["Tổng số tờ nhỏ"]) || 0), 0);
    const remainingTime = (targetCatTotal > 0 ? (remainingSmallCat / targetCatTotal) * 24 : 0) + (targetXaTotal > 0 ? (remainingSmallXa / targetXaTotal) * 24 : 0);


    // --- Bảng 2: Output ---
    const outputJobs = filteredJobs.filter(j => j["Output Ngày"] && formatDate(j["Output Ngày"]) === filters.date && (j["Trạng thái"] === 'Done' || j["Trạng thái"] === 'Partial'));
    const outputSmallTotal = outputJobs.reduce((sum, j) => sum + (Number(j["Tổng số tờ nhỏ"]) || 0), 0);
    const outputLargeTotal = outputJobs.reduce((sum, j) => sum + (Number(j["Tổng số tờ lớn"]) || 0), 0);


    const doneJobs = outputJobs.filter(j => j["Trạng thái"] === 'Done');
   
    let outputTimeCat = 0;
    doneJobs.filter(j => j.type === 'Cắt').forEach(job => {
        const workforce = job.Ca === 'Ca 1' ? nhanVienCaNgay : nhanVienCaDem;
        const targetPerPerson = TARGET_CAT_SHIFT / STANDARD_WORKFORCE;
        if (workforce > 0) {
            outputTimeCat += ((Number(job["Tổng số tờ nhỏ"]) || 0) / (workforce * targetPerPerson)) * 12;
        }
    });
   
    let outputTimeXa = 0;
    doneJobs.filter(j => j.type === 'Xả').forEach(job => {
        const workforce = job.Ca === 'Ca 1' ? nhanVienCaNgay : nhanVienCaDem;
        const targetPerPerson = TARGET_XA_SHIFT / STANDARD_WORKFORCE;
        if (workforce > 0) {
            outputTimeXa += ((Number(job["Tổng số tờ nhỏ"]) || 0) / (workforce * targetPerPerson)) * 12;
        }
    });
   
    const outputTime = outputTimeCat + outputTimeXa;
   
    // --- Downtime ---
    const downtimeLog = getSheetData(SHEET_DOWNTIME_LOG);
    let downtime = 0; // Placeholder for now


    return {
        nhanLuc: { ngay: nhanVienCaNgay, dem: nhanVienCaDem },
        targets: { cat: targetCatTotal, xa: targetXaTotal },
        plan: {
            totalSmall: planSmallCat + planSmallXa,
            totalLarge: planLargeCat + planLargeXa,
            pendingSmall: pendingSmallTotal,
            estimatedTime: planTime,
            remainingTime: remainingTime,
            downtime: downtime
        },
        output: {
            totalSmall: outputSmallTotal,
            totalLarge: outputLargeTotal,
            actualTime: outputTime,
            downtime: downtime
        }
    };
  } catch(e) {
    console.error(`getDashboardData Error: ${e.stack}`);
    return { error: e.message };
  }
}




// ===============================================================
// HÀM CHẨN ĐOÁN LỖI (DEBUGGING FUNCTION)
// ===============================================================

function testDataLoading() {
  try {
    console.log("=== Testing Data Loading ===");

    // Test sheet access
    const sheetCat = ss.getSheetByName(SHEET_CAT);
    const sheetXa = ss.getSheetByName(SHEET_XA);

    console.log(`Sheet ${SHEET_CAT} exists: ${!!sheetCat}`);
    console.log(`Sheet ${SHEET_XA} exists: ${!!sheetXa}`);

    if (sheetCat) {
      console.log(`${SHEET_CAT} has ${sheetCat.getLastRow()} rows`);
      if (sheetCat.getLastRow() > 0) {
        const headers = sheetCat.getRange(1, 1, 1, sheetCat.getLastColumn()).getValues()[0];
        console.log(`${SHEET_CAT} headers:`, headers);
      }
    }

    if (sheetXa) {
      console.log(`${SHEET_XA} has ${sheetXa.getLastRow()} rows`);
      if (sheetXa.getLastRow() > 0) {
        const headers = sheetXa.getRange(1, 1, 1, sheetXa.getLastColumn()).getValues()[0];
        console.log(`${SHEET_XA} headers:`, headers);
      }
    }

    // Test getInitialFilterData
    const filterData = getInitialFilterData();
    console.log("Filter data:", filterData);

    // Test getJobs with today's date
    const today = Utilities.formatDate(new Date(), Session.getScriptTimeZone(), 'yyyy-MM-dd');
    console.log(`Testing getJobs for today: ${today}`);

    const catJobs = getJobs('Cắt', today);
    const xaJobs = getJobs('Xả', today);

    console.log(`Cat jobs for ${today}:`, catJobs);
    console.log(`Xa jobs for ${today}:`, xaJobs);

    return {
      success: true,
      message: "Test completed. Check console logs for details.",
      filterData: filterData,
      catJobsCount: Array.isArray(catJobs) ? catJobs.length : 'Error',
      xaJobsCount: Array.isArray(xaJobs) ? xaJobs.length : 'Error'
    };

  } catch (e) {
    console.error("Test failed:", e);
    return {
      success: false,
      error: e.message,
      stack: e.stack
    };
  }
}
function runDiagnostics() {
  let logSheet = ss.getSheetByName(SHEET_DEBUG_LOG);
  if (logSheet) {
    logSheet.clear();
  } else {
    logSheet = ss.insertSheet(SHEET_DEBUG_LOG);
  }
 
  logSheet.appendRow(['Thời gian', 'Loại lỗi', 'Sheet', 'Dòng số', 'Chi tiết']);
 
  const log = (type, sheet, row, detail) => {
    logSheet.appendRow([new Date(), type, sheet, row, detail]);
  };


  log('INFO', 'Hệ thống', '', 'Bắt đầu kiểm tra chẩn đoán...');


  const sheetsToTest = [SHEET_CAT, SHEET_XA];
  const expectedHeaders = ["Kế hoạch Ngày", "Line", "Plan", "ID", "BO", "Máy in", "Stock code F", "Tổng số tờ nhỏ", "Tổng số tờ lớn", "Xả", "Stock size", "Note", "Trạng thái", "Tên nhân viên báo cáo", "Thời gian báo cáo", "Ca", "Output Ngày"];
  const numericColumns = ["Tổng số tờ nhỏ", "Tổng số tờ lớn"];
  const dateColumns = ["Kế hoạch Ngày", "Thời gian báo cáo", "Output Ngày"];


  sheetsToTest.forEach(sheetName => {
    const sheet = ss.getSheetByName(sheetName);
    if (!sheet) {
      log('ERROR', sheetName, '', `Sheet không tồn tại.`);
      return;
    }
   
    const data = sheet.getDataRange().getValues();
    if (data.length < 1) {
       log('INFO', sheetName, '', `Sheet trống.`);
       return;
    }
    const headers = data.shift();


    // 1. Kiểm tra tên cột
    expectedHeaders.forEach(expectedHeader => {
      if (!headers.includes(expectedHeader)) {
        log('ERROR', sheetName, 'Dòng 1', `Thiếu cột bắt buộc: "${expectedHeader}"`);
      }
    });


    // 2. Kiểm tra dữ liệu từng dòng
    data.forEach((row, index) => {
      const rowNum = index + 2; // +2 vì index bắt đầu từ 0 và đã bỏ qua header


      // Kiểm tra cột ngày tháng
      dateColumns.forEach(colName => {
        const colIndex = headers.indexOf(colName);
        if (colIndex > -1 && row[colIndex]) {
          if (formatDate(row[colIndex]) === '') {
            log('WARNING', sheetName, rowNum, `Cột "${colName}" có định dạng ngày không hợp lệ: "${row[colIndex]}"`);
          }
        }
      });


      // Kiểm tra cột số
      numericColumns.forEach(colName => {
        const colIndex = headers.indexOf(colName);
        if (colIndex > -1 && row[colIndex] && isNaN(Number(row[colIndex]))) {
           log('WARNING', sheetName, rowNum, `Cột "${colName}" có giá trị không phải là số: "${row[colIndex]}"`);
        }
      });
    });
  });


  log('INFO', 'Hệ thống', '', 'Hoàn tất kiểm tra chẩn đoán.');
  SpreadsheetApp.getUi().alert('Đã hoàn tất kiểm tra. Vui lòng xem kết quả trong sheet "DebugLog".');
}




// ===============================================================
// CÁC HÀM TỰ ĐỘNG (TRIGGER)
// ===============================================================


function checkDowntime() {
  try {
    const sheetCat = ss.getSheetByName(SHEET_CAT);
    const sheetXa = ss.getSheetByName(SHEET_XA);
    const logSheet = ss.getSheetByName(SHEET_DOWNTIME_LOG);
    if(!sheetCat || !sheetXa || !logSheet) return;


    const statusColIndexCat = sheetCat.getRange("A1:Q1").getValues()[0].indexOf("Trạng thái");
    const statusColIndexXa = sheetXa.getRange("A1:Q1").getValues()[0].indexOf("Trạng thái");


    if (statusColIndexCat === -1 || statusColIndexXa === -1) return;


    const dataCat = sheetCat.getLastRow() > 1 ? sheetCat.getRange(2, statusColIndexCat + 1, sheetCat.getLastRow() - 1).getValues().flat() : [];
    const dataXa = sheetXa.getLastRow() > 1 ? sheetXa.getRange(2, statusColIndexXa + 1, sheetXa.getLastRow() - 1).getValues().flat() : [];


    const hasPendingJobs = dataCat.includes("Chờ cắt") || dataXa.includes("Chờ cắt");
    const status = hasPendingJobs ? "Hoạt động" : "Dừng";
   
    logSheet.appendRow([new Date(), status]);


  } catch (e) {
    console.error("Lỗi khi kiểm tra downtime: " + e.message);
  }
}


function setupTrigger() {
  const allTriggers = ScriptApp.getProjectTriggers();
  for (const trigger of allTriggers) {
    if (trigger.getHandlerFunction() === "checkDowntime") {
      ScriptApp.deleteTrigger(trigger);
    }
  }


  ScriptApp.newTrigger("checkDowntime")
    .timeBased()
    .everyMinutes(15)
    .create();
 
  SpreadsheetApp.getUi().alert("Đã thiết lập thành công trigger kiểm tra downtime mỗi 15 phút.");
}



